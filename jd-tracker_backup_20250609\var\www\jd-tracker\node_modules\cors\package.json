{"name": "cors", "description": "Node.js CORS middleware", "version": "2.8.5", "author": "<PERSON> <<EMAIL>> (https://github.com/troygoode/)", "license": "MIT", "keywords": ["cors", "express", "connect", "middleware"], "repository": "expressjs/cors", "main": "./lib/index.js", "dependencies": {"object-assign": "^4", "vary": "^1"}, "devDependencies": {"after": "0.8.2", "eslint": "2.13.1", "express": "4.16.3", "mocha": "5.2.0", "nyc": "13.1.0", "supertest": "3.3.0"}, "files": ["lib/index.js", "CONTRIBUTING.md", "HISTORY.md", "LICENSE", "README.md"], "engines": {"node": ">= 0.10"}, "scripts": {"test": "npm run lint && nyc --reporter=html --reporter=text mocha --require test/support/env", "lint": "eslint lib test"}}