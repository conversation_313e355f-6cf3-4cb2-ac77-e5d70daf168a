{"name": "generate-function", "version": "2.3.1", "description": "Module that helps you write generated functions in Node", "main": "index.js", "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/generate-function"}, "keywords": ["generate", "code", "generation", "function", "performance"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/generate-function/issues"}, "homepage": "https://github.com/mafintosh/generate-function", "devDependencies": {"tape": "^4.9.1"}, "dependencies": {"is-property": "^1.0.2"}}