// 数据库连接配置
const mysql = require('mysql2');
const config = require('./config');

// 创建连接池
const pool = mysql.createPool({
  host: config.database.host,
  user: config.database.user,
  password: config.database.password,
  database: config.database.database,
  port: config.database.port,
  connectionLimit: config.database.connectionLimit,
  waitForConnections: config.database.waitForConnections,
  queueLimit: config.database.queueLimit,
  charset: 'utf8mb4'
});

// 创建Promise包装的连接池
const promisePool = pool.promise();

// 测试数据库连接
async function testConnection() {
  try {
    const connection = await promisePool.getConnection();
    console.log('数据库连接成功');
    connection.release();
  } catch (error) {
    console.error('数据库连接失败:', error.message);
    throw error;
  }
}

// 初始化数据库表
async function initializeTables() {
  try {
    const connection = await promisePool.getConnection();
    
    // 创建商品表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS products (
        id INT AUTO_INCREMENT PRIMARY KEY,
        sku VARCHAR(50) NOT NULL UNIQUE,
        url VARCHAR(500) NOT NULL,
        title VARCHAR(255) NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        isTracked BOOLEAN DEFAULT 0,
        marketPrice DECIMAL(10,2),
        marketPriceUpdate TIMESTAMP NULL,
        lastUpdated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_sku (sku),
        INDEX idx_tracked (isTracked),
        INDEX idx_updated (lastUpdated)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 创建价格历史表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS price_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        market_price DECIMAL(10,2),
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        INDEX idx_product_id (product_id),
        INDEX idx_recorded_at (recorded_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 创建用户跟踪表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_tracking (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id VARCHAR(50) NOT NULL,
        product_id INT NOT NULL,
        target_price DECIMAL(10,2),
        notify_enabled BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_product (user_id, product_id),
        INDEX idx_user_id (user_id),
        INDEX idx_product_id (product_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 创建系统日志表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS system_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        level ENUM('info', 'warn', 'error', 'debug') DEFAULT 'info',
        message TEXT NOT NULL,
        data JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_level (level),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    connection.release();
    console.log('数据库表初始化完成');
  } catch (error) {
    console.error('数据库表初始化失败:', error.message);
    throw error;
  }
}

// 数据库查询包装函数
const db = {
  // 获取连接池
  getPool: () => promisePool,
  
  // 执行查询
  query: async (sql, params = []) => {
    try {
      const [rows] = await promisePool.execute(sql, params);
      return rows;
    } catch (error) {
      console.error('数据库查询错误:', error);
      throw error;
    }
  },
  
  // 插入数据
  insert: async (table, data) => {
    try {
      const keys = Object.keys(data);
      const values = Object.values(data);
      const placeholders = keys.map(() => '?').join(',');
      
      const sql = `INSERT INTO ${table} (${keys.join(',')}) VALUES (${placeholders})`;
      const [result] = await promisePool.execute(sql, values);
      return result;
    } catch (error) {
      console.error('数据库插入错误:', error);
      throw error;
    }
  },
  
  // 更新数据
  update: async (table, data, where, whereParams = []) => {
    try {
      const keys = Object.keys(data);
      const values = Object.values(data);
      const setClause = keys.map(key => `${key} = ?`).join(',');
      
      const sql = `UPDATE ${table} SET ${setClause} WHERE ${where}`;
      const [result] = await promisePool.execute(sql, [...values, ...whereParams]);
      return result;
    } catch (error) {
      console.error('数据库更新错误:', error);
      throw error;
    }
  },
  
  // 删除数据
  delete: async (table, where, whereParams = []) => {
    try {
      const sql = `DELETE FROM ${table} WHERE ${where}`;
      const [result] = await promisePool.execute(sql, whereParams);
      return result;
    } catch (error) {
      console.error('数据库删除错误:', error);
      throw error;
    }
  },
  
  // 开始事务
  beginTransaction: async () => {
    const connection = await promisePool.getConnection();
    await connection.beginTransaction();
    return connection;
  },
  
  // 关闭连接池
  close: () => {
    return new Promise((resolve) => {
      pool.end(() => {
        console.log('数据库连接池已关闭');
        resolve();
      });
    });
  }
};

// 初始化数据库
async function initialize() {
  try {
    await testConnection();
    await initializeTables();
    console.log('数据库初始化完成');
  } catch (error) {
    console.error('数据库初始化失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，则初始化数据库
if (require.main === module) {
  initialize();
}

module.exports = {
  ...db,
  testConnection,
  initializeTables,
  initialize,
  pool: promisePool
};
